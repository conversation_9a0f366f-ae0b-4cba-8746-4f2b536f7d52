#!/usr/bin/env python3
"""
MouseYoke for MSFS 打包脚本
使用PyInstaller将项目打包为独立可执行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def clean_build():
    """清理之前的构建文件"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"已清理目录: {dir_name}")
    
    import glob
    for pattern in files_to_clean:
        for file in glob.glob(pattern):
            os.remove(file)
            print(f"已清理文件: {file}")

def build_executable():
    """构建可执行文件"""
    print("开始构建MouseYoke可执行文件...")
    
    # PyInstaller命令参数
    cmd = [
        'pyinstaller',
        '--onefile',                    # 打包成单个文件
        '--windowed',                   # 无控制台窗口
        '--name=MouseYoke_MSFS',        # 可执行文件名称
        '--icon=icon.ico',              # 图标文件（如果有的话）
        '--add-data=config.py;.',       # 包含配置文件
        '--hidden-import=pyvjoy',       # 确保包含pyvjoy
        '--hidden-import=keyboard',     # 确保包含keyboard
        '--hidden-import=pyautogui',    # 确保包含pyautogui
        '--clean',                      # 清理临时文件
        'main.py'                       # 主程序文件
    ]
    
    # 如果没有图标文件，移除图标参数
    if not os.path.exists('icon.ico'):
        cmd.remove('--icon=icon.ico')
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("构建成功！")
        print(result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    
    return True

def create_release_package():
    """创建发布包"""
    if not os.path.exists('dist/MouseYoke_MSFS.exe'):
        print("错误: 可执行文件不存在")
        return False
    
    # 创建发布目录
    release_dir = 'MouseYoke_MSFS_Release'
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    os.makedirs(release_dir)
    
    # 复制文件到发布目录
    files_to_copy = [
        ('dist/MouseYoke_MSFS.exe', 'MouseYoke_MSFS.exe'),
        ('README.md', 'README.md'),
        ('config.py', 'config.py'),
    ]
    
    for src, dst in files_to_copy:
        if os.path.exists(src):
            shutil.copy2(src, os.path.join(release_dir, dst))
            print(f"已复制: {src} -> {dst}")
    
    # 创建启动说明
    with open(os.path.join(release_dir, '使用说明.txt'), 'w', encoding='utf-8') as f:
        f.write("""MouseYoke for MSFS 使用说明

1. 安装要求：
   - 必须先安装vJoy驱动程序
   - 下载地址：http://vjoystick.sourceforge.net/
   - 运行vJoyConf.exe启用至少一个虚拟摇杆设备

2. 运行程序：
   - 双击 MouseYoke_MSFS.exe 启动程序
   - 如果遇到权限问题，请右键"以管理员身份运行"

3. 操作方法：
   - F1键：激活/锁定摇杆模式
   - ESC键：完全退出摇杆模式
   - 在激活模式下移动鼠标控制摇杆

4. 配置调整：
   - 编辑config.py文件可调整死区等参数
   - 修改后需要重启程序生效

5. 故障排除：
   - 如果提示找不到vJoy设备，请检查vJoy驱动安装
   - 详细说明请参考README.md文件

享受您的飞行体验！
""")
    
    print(f"发布包已创建: {release_dir}/")
    return True

def main():
    """主函数"""
    print("MouseYoke for MSFS 打包工具")
    print("=" * 40)
    
    # 检查是否在正确的目录
    if not os.path.exists('main.py'):
        print("错误: 请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 清理之前的构建
    clean_build()
    
    # 构建可执行文件
    if not build_executable():
        print("构建失败，退出")
        sys.exit(1)
    
    # 创建发布包
    if create_release_package():
        print("\n✅ 打包完成！")
        print("📦 发布文件位于: MouseYoke_MSFS_Release/")
        print("🚀 可以将整个文件夹分发给用户")
    else:
        print("❌ 创建发布包失败")

if __name__ == "__main__":
    main()
