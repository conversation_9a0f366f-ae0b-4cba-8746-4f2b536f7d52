[Setup]
AppName=MouseYoke for MSFS
AppVersion=1.4
AppPublisher=Your Name
AppPublisherURL=https://github.com/yourusername/mouseyoke-msfs
DefaultDirName={autopf}\MouseYoke for MSFS
DefaultGroupName=MouseYoke for MSFS
OutputDir=installer_output
OutputBaseFilename=MouseYoke_MSFS_Setup
Compression=lzma
SolidCompression=yes
WizardStyle=modern

[Languages]
Name: "chinesesimp"; MessagesFile: "compiler:Languages\ChineseSimplified.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "MouseYoke_MSFS_Release\MouseYoke_MSFS.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "MouseYoke_MSFS_Release\config.py"; DestDir: "{app}"; Flags: ignoreversion
Source: "MouseYoke_MSFS_Release\README.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "MouseYoke_MSFS_Release\使用说明.txt"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
Name: "{group}\MouseYoke for MSFS"; Filename: "{app}\MouseYoke_MSFS.exe"
Name: "{group}\{cm:UninstallProgram,MouseYoke for MSFS}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\MouseYoke for MSFS"; Filename: "{app}\MouseYoke_MSFS.exe"; Tasks: desktopicon

[Run]
Filename: "{app}\MouseYoke_MSFS.exe"; Description: "{cm:LaunchProgram,MouseYoke for MSFS}"; Flags: nowait postinstall skipifsilent
