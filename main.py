import sys
import keyboard
import pyautogui
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from overlay import OverlayWindow
from input_mapper import InputMapper
from config import Config

class MouseYoke:
    def __init__(self):
        self.active = False
        self.last_mouse_pos = (0, 0)
        self.app = QApplication(sys.argv)
        self.overlay = OverlayWindow()

        # 获取真实屏幕尺寸
        self.screen_width = self.overlay.width()
        self.screen_height = self.overlay.height()

        # 将屏幕尺寸传递给input_mapper
        self.input_mapper = InputMapper(self.screen_width, self.screen_height)

        # If input mapper failed to init, don't bother setting up the rest
        if not self.input_mapper.device:
            return

        # 注册热键
        keyboard.add_hotkey('f1', self.toggle_active)

        # 设置定时器来持续更新摇杆状态
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_yoke)
        self.timer.start(16)  # ~60 FPS

    def toggle_active(self):
        """切换激活状态"""
        if not self.input_mapper.device:
            return

        if not self.active:
            # 进入激活模式
            self.last_mouse_pos = pyautogui.position()
            pyautogui.moveTo(self.overlay.center_x, self.overlay.center_y)
            self.active = True
            self.overlay.show_active()
        else:
            # 退出激活模式
            current_pos = pyautogui.position()
            self.overlay.show_locked(current_pos[0], current_pos[1])
            pyautogui.moveTo(self.last_mouse_pos[0], self.last_mouse_pos[1])
            self.active = False
            self.input_mapper.reset() # 重置摇杆位置

    def update_yoke(self):
        """如果激活，则根据鼠标位置更新摇杆"""
        if self.active:
            pos = pyautogui.position()
            self.input_mapper.map_position(pos[0], pos[1])

    def run(self):
        """运行主循环"""
        # 如果vJoy设备未初始化，input_mapper会打印详细错误，这里只做最后检查
        if not self.input_mapper.device:
            # The app will exit gracefully as the Qt loop won't start
            return

        self.overlay.show()
        sys.exit(self.app.exec_())

if __name__ == "__main__":
    yoke = MouseYoke()
    yoke.run()
