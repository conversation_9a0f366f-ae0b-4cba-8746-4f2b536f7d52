# MouseYoke for MSFS

一个将鼠标转换为虚拟摇杆的工具，专为Microsoft Flight Simulator (MSFS) 设计。通过鼠标在屏幕上的移动来控制飞机的俯仰和滚转，提供更直观的飞行体验。

## 功能特点

### 🎮 核心功能
- **全屏幕操作范围** - 使用整个屏幕作为摇杆操作区域，提供最大的控制精度
- **自适应屏幕尺寸** - 自动检测并适配任何分辨率的屏幕
- **实时摇杆输出** - 通过vJoy驱动将鼠标移动转换为标准摇杆信号

### 🔄 智能状态管理
- **激活模式** - 鼠标控制摇杆，实时响应
- **锁定模式** - 摇杆保持在指定位置，鼠标可自由移动
- **中心死区** - 在屏幕中心附近锁定时自动回正摇杆

### 🎯 视觉反馈
- **中心十字架** - 白色十字架标示屏幕中心（摇杆中性位置）
- **锁定十字架** - 黄色十字架显示当前锁定的摇杆位置
- **透明覆盖层** - 不干扰其他应用程序的使用

## 系统要求

### 必需软件
- **Python 3.7+**
- **vJoy驱动** - 虚拟摇杆驱动程序
- **Microsoft Flight Simulator** 或其他支持摇杆输入的飞行模拟器

### Python依赖包
```
PyQt5
pyautogui
keyboard
pyvjoy
```

## 安装步骤

### 1. 安装vJoy驱动
1. 下载并安装 [vJoy驱动](http://vjoystick.sourceforge.net/)
2. 运行 `vJoyConf.exe` 配置工具
3. 启用至少一个虚拟摇杆设备（建议使用设备#1）
4. 确保设备状态显示为"OK"

### 2. 安装Python依赖
```bash
pip install PyQt5 pyautogui keyboard pyvjoy
```

### 3. 运行程序
```bash
python main.py
```

## 使用方法

### 基本操作

#### 🔑 热键控制
- **F1** - 激活/锁定模式切换
- **ESC** - 完全退出摇杆模式

#### 📱 操作流程
1. **启动程序** - 运行 `python main.py`
2. **按F1激活** - 鼠标跳转到屏幕中心，进入激活模式
3. **移动鼠标** - 在屏幕上移动鼠标控制摇杆
4. **按F1锁定** - 锁定当前摇杆位置，鼠标恢复自由移动
5. **按F1重新激活** - 鼠标跳回锁定位置，继续控制
6. **按ESC退出** - 完全退出摇杆模式，重置所有状态

### 高级功能

#### 🎯 中心死区功能
- 当在屏幕中心附近的小范围内锁定摇杆时
- 摇杆会自动回正到中心位置
- 相当于自动进入未激活状态
- 适用于需要快速回中的场景

#### ⚙️ 自定义配置
编辑 `config.py` 文件可调整以下参数：

```python
# 死区参数
DEAD_ZONE_FACTOR = 0.002        # 常规死区大小
CENTER_DEAD_ZONE_FACTOR = 0.01  # 中心死区大小

# 视觉样式
CENTER_CROSS_SIZE = 20          # 中心十字架大小
LOCKED_CROSS_SIZE = 15          # 锁定十字架大小
```

## 技术原理

### 坐标映射
- **屏幕中心** → 摇杆中性位置 (16384, 16384)
- **屏幕边缘** → 摇杆最大偏移 (0-32767)
- **比例映射** → 保持屏幕宽高比，避免控制变形

### 状态管理
```
未激活 ──F1──→ 激活模式 ──F1──→ 锁定模式
   ↑                              │
   └──────────F1──────────────────┘
   
任何状态 ──ESC──→ 完全退出
```

## 故障排除

### 常见问题

#### vJoy设备未找到
```
vJoy 初始化失败: 未能找到任何可用的vJoy设备
```
**解决方案：**
1. 确认已正确安装vJoy驱动
2. 运行vJoyConf.exe，启用至少一个设备
3. 重启计算机
4. 检查设备是否被其他程序占用

#### 程序无响应
**解决方案：**
1. 检查是否有杀毒软件阻止键盘钩子
2. 以管理员权限运行程序
3. 确认PyQt5正确安装

#### 摇杆控制不准确
**解决方案：**
1. 调整config.py中的死区参数
2. 检查屏幕分辨率是否正确检测
3. 确认MSFS中摇杆设置正确

## 文件结构

```
MouseYoke for MSFS/
├── main.py           # 主程序入口
├── overlay.py        # 覆盖层显示
├── input_mapper.py   # 输入映射逻辑
├── config.py         # 配置参数
└── README.md         # 说明文档
```

## 开发信息

### 版本历史
- **v1.0** - 基础鼠标转摇杆功能
- **v1.1** - 添加自适应屏幕尺寸
- **v1.2** - 增加激活/锁定状态切换
- **v1.3** - 优化鼠标移动行为
- **v1.4** - 添加中心死区功能

### 技术栈
- **Python 3** - 主要开发语言
- **PyQt5** - GUI框架和覆盖层
- **vJoy** - 虚拟摇杆驱动
- **pyautogui** - 鼠标控制
- **keyboard** - 全局热键监听

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

**享受您的飞行体验！** ✈️
